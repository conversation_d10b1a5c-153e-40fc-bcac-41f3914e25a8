"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_bootstrap_tsx"],{

/***/ "./app/bootstrap.tsx":
/*!***************************!*\
  !*** ./app/bootstrap.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Agents: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.Agents),\n/* harmony export */   App: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.App),\n/* harmony export */   Home: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.Home),\n/* harmony export */   MicroFrontendDemo: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.MicroFrontendDemo),\n/* harmony export */   NewAgent: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.NewAgent),\n/* harmony export */   RouterExample: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.RouterExample),\n/* harmony export */   SimpleTestComponent: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.SimpleTestComponent),\n/* harmony export */   apiSlice: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.apiSlice),\n/* harmony export */   authReducer: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.authReducer),\n/* harmony export */   routes: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.routes),\n/* harmony export */   selectCurrentAccessToken: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.selectCurrentAccessToken),\n/* harmony export */   selectCurrentAuthState: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.selectCurrentAuthState),\n/* harmony export */   selectCurrentLoginStatus: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.selectCurrentLoginStatus),\n/* harmony export */   selectCurrentRefreshToken: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.selectCurrentRefreshToken),\n/* harmony export */   setCredentials: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.setCredentials),\n/* harmony export */   store: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.store),\n/* harmony export */   unsetCredentials: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.unsetCredentials),\n/* harmony export */   useAppDispatch: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.useAppSelector),\n/* harmony export */   withReduxProvider: () => (/* reexport safe */ _federation_entry__WEBPACK_IMPORTED_MODULE_4__.withReduxProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom/client */ \"./node_modules/react-dom/client.js\");\n/* harmony import */ var _root__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./root */ \"./app/root.tsx\");\n/* harmony import */ var _app_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app.css */ \"./app/app.css\");\n/* harmony import */ var _federation_entry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./federation-entry */ \"./app/federation-entry.tsx\");\n/**\n * Bootstrap file for the micro-frontend.\n * This file is used when the micro-frontend is loaded in standalone mode.\n * When loaded as a federated module, the host application will control the bootstrapping.\n */\n\n\n\n\n\n\n// Check if we're running in standalone mode or as a federated module\nvar isFederated = window.location.search.includes(\"federated=true\");\n\n// Only render the app if we're in standalone mode\nif (!isFederated) {\n  var rootElement = document.getElementById(\"root\");\n  if (rootElement) {\n    react_dom_client__WEBPACK_IMPORTED_MODULE_1__.createRoot(rootElement).render(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().StrictMode), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_root__WEBPACK_IMPORTED_MODULE_2__[\"default\"], null)));\n  }\n}\n\n// Export the components and utilities for federation\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/bootstrap.tsx\n");

/***/ }),

/***/ "./app/components/MicroFrontendDemo.tsx":
/*!**********************************************!*\
  !*** ./app/components/MicroFrontendDemo.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MicroFrontendDemo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n/**\n * A simple demo component to verify that the micro-frontend integration is working correctly.\n * This component includes:\n * - State management with React hooks\n * - Styling with Tailwind CSS\n * - Interactive elements\n */\nfunction MicroFrontendDemo() {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"Hello from Bot UI Micro-Frontend!\"),\n    _useState4 = _slicedToArray(_useState3, 2),\n    message = _useState4[0],\n    setMessage = _useState4[1];\n  var incrementCount = function incrementCount() {\n    setCount(count + 1);\n  };\n  var changeMessage = function changeMessage() {\n    var messages = [\"Hello from Bot UI Micro-Frontend!\", \"Integration successful!\", \"Module Federation is working!\", \"You can now use all components from this micro-frontend!\", \"Congratulations on setting up your micro-frontend architecture!\"];\n    var randomIndex = Math.floor(Math.random() * messages.length);\n    setMessage(messages[randomIndex]);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"p-6 max-w-md mx-auto bg-white rounded-xl shadow-md flex flex-col items-center space-y-4 mt-4\"\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"text-center\"\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-2xl font-bold text-blue-600\"\n  }, \"Micro-Frontend Demo\"), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-gray-500 mt-2\"\n  }, \"This component is loaded from the Bot UI micro-frontend\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"bg-blue-100 p-4 rounded-lg w-full text-center\"\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-lg font-semibold\"\n  }, message)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex flex-col items-center space-y-2 w-full\"\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-gray-700\"\n  }, \"Counter: \", /*#__PURE__*/React.createElement(\"span\", {\n    className: \"font-bold\"\n  }, count)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex space-x-2\"\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: incrementCount,\n    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\"\n  }, \"Increment\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: changeMessage,\n    className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors\"\n  }, \"Change Message\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"border-t border-gray-200 pt-4 w-full\"\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-sm text-gray-500 text-center\"\n  }, \"Current time: \", new Date().toLocaleTimeString())));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/MicroFrontendDemo.tsx\n");

/***/ }),

/***/ "./app/components/SimpleTestComponent.tsx":
/*!************************************************!*\
  !*** ./app/components/SimpleTestComponent.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleTestComponent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * A very simple component to test the micro-frontend integration and Tailwind CSS\n * This component has minimal dependencies and should be easy to load\n */\nfunction SimpleTestComponent() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-5 m-5 border-2 border-blue-500 rounded-lg bg-blue-50\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h2\", {\n    className: \"text-blue-600 text-xl font-bold mb-2\"\n  }, \"Simple Test Component\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-gray-700 mb-2\"\n  }, \"This component is loaded from the Bot UI micro-frontend.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-gray-700 mb-2\"\n  }, \"If you can see this styled with Tailwind, the integration is working!\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-gray-600 text-sm\"\n  }, \"Current time: \", new Date().toLocaleTimeString()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mt-4 p-3 bg-green-100 border border-green-300 rounded\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-green-800 font-medium\"\n  }, \"\\u2705 Tailwind CSS is working if this box is green!\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mt-2 flex space-x-2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\"\n  }, \"Test Button\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors\"\n  }, \"Another Button\")));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/SimpleTestComponent.tsx\n");

/***/ }),

/***/ "./app/federation-entry.tsx":
/*!**********************************!*\
  !*** ./app/federation-entry.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Agents: () => (/* reexport safe */ _wrappers_AgentsWrapper__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   App: () => (/* reexport safe */ _root__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _wrappers_HomeWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   MicroFrontendDemo: () => (/* reexport safe */ _components_MicroFrontendDemo__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   NewAgent: () => (/* reexport safe */ _wrappers_NewAgentWrapper__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   RouterExample: () => (/* reexport safe */ _components_RouterExample__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   SimpleTestComponent: () => (/* reexport safe */ _components_SimpleTestComponent__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   apiSlice: () => (/* reexport safe */ _redux_apiSlice__WEBPACK_IMPORTED_MODULE_12__.apiSlice),\n/* harmony export */   authReducer: () => (/* reexport safe */ _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   routes: () => (/* reexport safe */ _router_routes__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   selectCurrentAccessToken: () => (/* reexport safe */ _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__.selectCurrentAccessToken),\n/* harmony export */   selectCurrentAuthState: () => (/* reexport safe */ _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__.selectCurrentAuthState),\n/* harmony export */   selectCurrentLoginStatus: () => (/* reexport safe */ _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__.selectCurrentLoginStatus),\n/* harmony export */   selectCurrentRefreshToken: () => (/* reexport safe */ _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__.selectCurrentRefreshToken),\n/* harmony export */   setCredentials: () => (/* reexport safe */ _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__.setCredentials),\n/* harmony export */   store: () => (/* reexport safe */ _redux_store__WEBPACK_IMPORTED_MODULE_2__.store),\n/* harmony export */   unsetCredentials: () => (/* reexport safe */ _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__.unsetCredentials),\n/* harmony export */   useAppDispatch: () => (/* reexport safe */ _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_5__.useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* reexport safe */ _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_5__.useAppSelector),\n/* harmony export */   withReduxProvider: () => (/* binding */ withReduxProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ \"webpack/sharing/consume/default/react-redux/react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./redux/store */ \"./app/redux/store.ts\");\n/* harmony import */ var _root__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./root */ \"./app/root.tsx\");\n/* harmony import */ var _router_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./router/routes */ \"./app/router/routes.ts\");\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _components_RouterExample__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/RouterExample */ \"./app/components/RouterExample.tsx\");\n/* harmony import */ var _components_MicroFrontendDemo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/MicroFrontendDemo */ \"./app/components/MicroFrontendDemo.tsx\");\n/* harmony import */ var _components_SimpleTestComponent__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/SimpleTestComponent */ \"./app/components/SimpleTestComponent.tsx\");\n/* harmony import */ var _wrappers_HomeWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./wrappers/HomeWrapper */ \"./app/wrappers/HomeWrapper.tsx\");\n/* harmony import */ var _wrappers_AgentsWrapper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./wrappers/AgentsWrapper */ \"./app/wrappers/AgentsWrapper.tsx\");\n/* harmony import */ var _wrappers_NewAgentWrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./wrappers/NewAgentWrapper */ \"./app/wrappers/NewAgentWrapper.tsx\");\n/* harmony import */ var _redux_apiSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./redux/apiSlice */ \"./app/redux/apiSlice.ts\");\n/* harmony import */ var _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./redux/auth/authSlice */ \"./app/redux/auth/authSlice.ts\");\n/**\n * This is the main entry point for the micro-frontend when used in a federated setup.\n * It exports all the components, routes, and utilities that should be available to the host application.\n */\n\n\n\n\n\n// Export the main App component\n\n\n// Export routes configuration\n\n\n// Export Redux store\n\n// Export Redux hooks\n\n\n// Export components\n\n\n\n\n// Export wrapped route components\n\n\n\n\n// Export API slice for data fetching\n\n\n// Export auth slice for authentication\n\n\n// Export a higher-order component for wrapping components with Redux Provider\nvar withReduxProvider = function withReduxProvider(Component) {\n  return function (props) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n      store: _redux_store__WEBPACK_IMPORTED_MODULE_2__.store\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Component, props));\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/federation-entry.tsx\n");

/***/ }),

/***/ "./app/wrappers/AgentsWrapper.tsx":
/*!****************************************!*\
  !*** ./app/wrappers/AgentsWrapper.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ \"webpack/sharing/consume/default/react-redux/react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../redux/store */ \"./app/redux/store.ts\");\n/* harmony import */ var _routes_Agents_index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../routes/Agents/index */ \"./app/routes/Agents/index.tsx\");\n\n\n\n\nvar AgentsWrapper = function AgentsWrapper() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n    store: _redux_store__WEBPACK_IMPORTED_MODULE_2__.store\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Agents_index__WEBPACK_IMPORTED_MODULE_3__[\"default\"], null));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgentsWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvd3JhcHBlcnMvQWdlbnRzV3JhcHBlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNhO0FBQ0E7QUFDYztBQUVyRCxJQUFNSSxhQUF1QixHQUFHLFNBQTFCQSxhQUF1QkEsQ0FBQTtFQUFBLG9CQUMzQkosMERBQUEsQ0FBQ0MsaURBQVE7SUFBQ0MsS0FBSyxFQUFFQSwrQ0FBS0E7RUFBQyxnQkFDckJGLDBEQUFBLENBQUNHLDREQUFlLE1BQUUsQ0FDVixDQUFDO0FBQUEsQ0FDWjtBQUVELGlFQUFlQyxhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL3dyYXBwZXJzL0FnZW50c1dyYXBwZXIudHN4PzNhYWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcbmltcG9ydCB7IHN0b3JlIH0gZnJvbSBcIi4uL3JlZHV4L3N0b3JlXCI7XG5pbXBvcnQgQWdlbnRzQ29tcG9uZW50IGZyb20gXCIuLi9yb3V0ZXMvQWdlbnRzL2luZGV4XCI7XG5cbmNvbnN0IEFnZW50c1dyYXBwZXI6IFJlYWN0LkZDID0gKCkgPT4gKFxuICA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT5cbiAgICA8QWdlbnRzQ29tcG9uZW50IC8+XG4gIDwvUHJvdmlkZXI+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBBZ2VudHNXcmFwcGVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUHJvdmlkZXIiLCJzdG9yZSIsIkFnZW50c0NvbXBvbmVudCIsIkFnZW50c1dyYXBwZXIiLCJjcmVhdGVFbGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/wrappers/AgentsWrapper.tsx\n");

/***/ }),

/***/ "./app/wrappers/HomeWrapper.tsx":
/*!**************************************!*\
  !*** ./app/wrappers/HomeWrapper.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ \"webpack/sharing/consume/default/react-redux/react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../redux/store */ \"./app/redux/store.ts\");\n/* harmony import */ var _routes_Home_index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../routes/Home/index */ \"./app/routes/Home/index.tsx\");\n\n\n\n\nvar HomeWrapper = function HomeWrapper() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n    store: _redux_store__WEBPACK_IMPORTED_MODULE_2__.store\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Home_index__WEBPACK_IMPORTED_MODULE_3__[\"default\"], null));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvd3JhcHBlcnMvSG9tZVdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBMEI7QUFDYTtBQUNBO0FBQ1U7QUFFakQsSUFBTUksV0FBcUIsR0FBRyxTQUF4QkEsV0FBcUJBLENBQUE7RUFBQSxvQkFDekJKLDBEQUFBLENBQUNDLGlEQUFRO0lBQUNDLEtBQUssRUFBRUEsK0NBQUtBO0VBQUMsZ0JBQ3JCRiwwREFBQSxDQUFDRywwREFBYSxNQUFFLENBQ1IsQ0FBQztBQUFBLENBQ1o7QUFFRCxpRUFBZUMsV0FBVyIsInNvdXJjZXMiOlsid2VicGFjazovL2JvdC11aS8uL2FwcC93cmFwcGVycy9Ib21lV3JhcHBlci50c3g/ZWVlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcm92aWRlciB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xuaW1wb3J0IHsgc3RvcmUgfSBmcm9tIFwiLi4vcmVkdXgvc3RvcmVcIjtcbmltcG9ydCBIb21lQ29tcG9uZW50IGZyb20gXCIuLi9yb3V0ZXMvSG9tZS9pbmRleFwiO1xuXG5jb25zdCBIb21lV3JhcHBlcjogUmVhY3QuRkMgPSAoKSA9PiAoXG4gIDxQcm92aWRlciBzdG9yZT17c3RvcmV9PlxuICAgIDxIb21lQ29tcG9uZW50IC8+XG4gIDwvUHJvdmlkZXI+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBIb21lV3JhcHBlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlByb3ZpZGVyIiwic3RvcmUiLCJIb21lQ29tcG9uZW50IiwiSG9tZVdyYXBwZXIiLCJjcmVhdGVFbGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/wrappers/HomeWrapper.tsx\n");

/***/ }),

/***/ "./app/wrappers/NewAgentWrapper.tsx":
/*!******************************************!*\
  !*** ./app/wrappers/NewAgentWrapper.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ \"webpack/sharing/consume/default/react-redux/react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../redux/store */ \"./app/redux/store.ts\");\n/* harmony import */ var _routes_Agents_new__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../routes/Agents/new */ \"./app/routes/Agents/new.tsx\");\n\n\n\n\nvar NewAgentWrapper = function NewAgentWrapper() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n    store: _redux_store__WEBPACK_IMPORTED_MODULE_2__.store\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Agents_new__WEBPACK_IMPORTED_MODULE_3__[\"default\"], null));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewAgentWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvd3JhcHBlcnMvTmV3QWdlbnRXcmFwcGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBQ2E7QUFDQTtBQUNjO0FBRXJELElBQU1JLGVBQXlCLEdBQUcsU0FBNUJBLGVBQXlCQSxDQUFBO0VBQUEsb0JBQzdCSiwwREFBQSxDQUFDQyxpREFBUTtJQUFDQyxLQUFLLEVBQUVBLCtDQUFLQTtFQUFDLGdCQUNyQkYsMERBQUEsQ0FBQ0csMERBQWlCLE1BQUUsQ0FDWixDQUFDO0FBQUEsQ0FDWjtBQUVELGlFQUFlQyxlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL3dyYXBwZXJzL05ld0FnZW50V3JhcHBlci50c3g/NzkwZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcm92aWRlciB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xuaW1wb3J0IHsgc3RvcmUgfSBmcm9tIFwiLi4vcmVkdXgvc3RvcmVcIjtcbmltcG9ydCBOZXdBZ2VudENvbXBvbmVudCBmcm9tIFwiLi4vcm91dGVzL0FnZW50cy9uZXdcIjtcblxuY29uc3QgTmV3QWdlbnRXcmFwcGVyOiBSZWFjdC5GQyA9ICgpID0+IChcbiAgPFByb3ZpZGVyIHN0b3JlPXtzdG9yZX0+XG4gICAgPE5ld0FnZW50Q29tcG9uZW50IC8+XG4gIDwvUHJvdmlkZXI+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBOZXdBZ2VudFdyYXBwZXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJQcm92aWRlciIsInN0b3JlIiwiTmV3QWdlbnRDb21wb25lbnQiLCJOZXdBZ2VudFdyYXBwZXIiLCJjcmVhdGVFbGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/wrappers/NewAgentWrapper.tsx\n");

/***/ }),

/***/ "./node_modules/react-dom/client.js":
/*!******************************************!*\
  !*** ./node_modules/react-dom/client.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar m = __webpack_require__(/*! react-dom */ \"webpack/sharing/consume/default/react-dom/react-dom\");\nif (false) // removed by dead control flow\n{} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtZG9tL2NsaWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixRQUFRLG1CQUFPLENBQUMsc0VBQVc7QUFDM0IsSUFBSSxLQUFxQyxFQUFFO0FBQUEsRUFHMUMsQ0FBQztBQUNGO0FBQ0EsRUFBRSxrQkFBa0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLEVBQUUsbUJBQW1CO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JvdC11aS8uL25vZGVfbW9kdWxlcy9yZWFjdC1kb20vY2xpZW50LmpzPzExYjEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgbSA9IHJlcXVpcmUoJ3JlYWN0LWRvbScpO1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgZXhwb3J0cy5jcmVhdGVSb290ID0gbS5jcmVhdGVSb290O1xuICBleHBvcnRzLmh5ZHJhdGVSb290ID0gbS5oeWRyYXRlUm9vdDtcbn0gZWxzZSB7XG4gIHZhciBpID0gbS5fX1NFQ1JFVF9JTlRFUk5BTFNfRE9fTk9UX1VTRV9PUl9ZT1VfV0lMTF9CRV9GSVJFRDtcbiAgZXhwb3J0cy5jcmVhdGVSb290ID0gZnVuY3Rpb24oYywgbykge1xuICAgIGkudXNpbmdDbGllbnRFbnRyeVBvaW50ID0gdHJ1ZTtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIG0uY3JlYXRlUm9vdChjLCBvKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgaS51c2luZ0NsaWVudEVudHJ5UG9pbnQgPSBmYWxzZTtcbiAgICB9XG4gIH07XG4gIGV4cG9ydHMuaHlkcmF0ZVJvb3QgPSBmdW5jdGlvbihjLCBoLCBvKSB7XG4gICAgaS51c2luZ0NsaWVudEVudHJ5UG9pbnQgPSB0cnVlO1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gbS5oeWRyYXRlUm9vdChjLCBoLCBvKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgaS51c2luZ0NsaWVudEVudHJ5UG9pbnQgPSBmYWxzZTtcbiAgICB9XG4gIH07XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-dom/client.js\n");

/***/ })

}]);