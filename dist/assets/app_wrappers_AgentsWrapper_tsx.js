"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_wrappers_AgentsWrapper_tsx"],{

/***/ "./app/routes/Agents/index.tsx":
/*!*************************************!*\
  !*** ./app/routes/Agents/index.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Agents)\n/* harmony export */ });\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-router-dom */ \"webpack/sharing/consume/default/react-router-dom/react-router-dom\");\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Agents() {\n  return /*#__PURE__*/React.createElement(\"main\", {\n    className: \"flex items-center justify-center pt-16 pb-4\"\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex-1 flex flex-col items-center gap-16 min-h-0\"\n  }, /*#__PURE__*/React.createElement(\"header\", {\n    className: \"flex flex-col items-center gap-9\"\n  }, \"Agents\"), /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Outlet, null)));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvcm91dGVzL0FnZW50cy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBDO0FBRTNCLFNBQVNDLE1BQU1BLENBQUEsRUFBRztFQUMvQixvQkFDRUMsS0FBQSxDQUFBQyxhQUFBO0lBQU1DLFNBQVMsRUFBQztFQUE2QyxnQkFDM0RGLEtBQUEsQ0FBQUMsYUFBQTtJQUFLQyxTQUFTLEVBQUM7RUFBa0QsZ0JBQy9ERixLQUFBLENBQUFDLGFBQUE7SUFBUUMsU0FBUyxFQUFDO0VBQWtDLEdBQUMsUUFBYyxDQUFDLGVBRXBFRixLQUFBLENBQUFDLGFBQUEsQ0FBQ0gsb0RBQU0sTUFBRSxDQUNOLENBQ0QsQ0FBQztBQUVYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL3JvdXRlcy9BZ2VudHMvaW5kZXgudHN4P2NjYjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgT3V0bGV0IH0gZnJvbSBcInJlYWN0LXJvdXRlci1kb21cIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWdlbnRzKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB0LTE2IHBiLTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0xNiBtaW4taC0wXCI+XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTlcIj5BZ2VudHM8L2hlYWRlcj5cblxuICAgICAgICA8T3V0bGV0IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiT3V0bGV0IiwiQWdlbnRzIiwiUmVhY3QiLCJjcmVhdGVFbGVtZW50IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/routes/Agents/index.tsx\n");

/***/ }),

/***/ "./app/wrappers/AgentsWrapper.tsx":
/*!****************************************!*\
  !*** ./app/wrappers/AgentsWrapper.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ \"webpack/sharing/consume/default/react-redux/react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../redux/store */ \"./app/redux/store.ts\");\n/* harmony import */ var _routes_Agents_index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../routes/Agents/index */ \"./app/routes/Agents/index.tsx\");\n\n\n\n\nvar AgentsWrapper = function AgentsWrapper() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n    store: _redux_store__WEBPACK_IMPORTED_MODULE_2__.store\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Agents_index__WEBPACK_IMPORTED_MODULE_3__[\"default\"], null));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgentsWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvd3JhcHBlcnMvQWdlbnRzV3JhcHBlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNhO0FBQ0E7QUFDYztBQUVyRCxJQUFNSSxhQUF1QixHQUFHLFNBQTFCQSxhQUF1QkEsQ0FBQTtFQUFBLG9CQUMzQkosMERBQUEsQ0FBQ0MsaURBQVE7SUFBQ0MsS0FBSyxFQUFFQSwrQ0FBS0E7RUFBQyxnQkFDckJGLDBEQUFBLENBQUNHLDREQUFlLE1BQUUsQ0FDVixDQUFDO0FBQUEsQ0FDWjtBQUVELGlFQUFlQyxhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL3dyYXBwZXJzL0FnZW50c1dyYXBwZXIudHN4PzNhYWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcbmltcG9ydCB7IHN0b3JlIH0gZnJvbSBcIi4uL3JlZHV4L3N0b3JlXCI7XG5pbXBvcnQgQWdlbnRzQ29tcG9uZW50IGZyb20gXCIuLi9yb3V0ZXMvQWdlbnRzL2luZGV4XCI7XG5cbmNvbnN0IEFnZW50c1dyYXBwZXI6IFJlYWN0LkZDID0gKCkgPT4gKFxuICA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT5cbiAgICA8QWdlbnRzQ29tcG9uZW50IC8+XG4gIDwvUHJvdmlkZXI+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBBZ2VudHNXcmFwcGVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUHJvdmlkZXIiLCJzdG9yZSIsIkFnZW50c0NvbXBvbmVudCIsIkFnZW50c1dyYXBwZXIiLCJjcmVhdGVFbGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/wrappers/AgentsWrapper.tsx\n");

/***/ })

}]);