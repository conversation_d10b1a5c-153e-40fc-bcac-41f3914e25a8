/**
 * This is the main entry point for the micro-frontend when used in a federated setup.
 * It exports all the components, routes, and utilities that should be available to the host application.
 */

import React from "react";
import { Provider } from "react-redux";
import { store } from "./redux/store";

// Export the main App component
export { default as App } from "./root";

// Export routes configuration
export { default as routes } from "./router/routes";

// Export Redux store
export { store } from "./redux/store";
export type { RootState, AppDispatch } from "./redux/store";

// Export Redux hooks
export { useAppDispatch, useAppSelector } from "./hooks/redux-hooks";

// Export components
export { default as RouterExample } from "./components/RouterExample";
export { default as MicroFrontendDemo } from "./components/MicroFrontendDemo";
export { default as SimpleTestComponent } from "./components/SimpleTestComponent";

// Export wrapped route components
export { default as Home } from "./wrappers/HomeWrapper";
export { default as Agents } from "./wrappers/AgentsWrapper";
export { default as NewAgent } from "./wrappers/NewAgentWrapper";

// Export API slice for data fetching
export { apiSlice } from "./redux/apiSlice";

// Export auth slice for authentication
export {
  default as authReducer,
  setCredentials,
  unsetCredentials,
  selectCurrentLoginStatus,
  selectCurrentAccessToken,
  selectCurrentRefreshToken,
  selectCurrentAuthState,
} from "./redux/auth/authSlice";

// Export a higher-order component for wrapping components with Redux Provider
export const withReduxProvider = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => (
    <Provider store={store}>
      <Component {...props} />
    </Provider>
  );
};
